import type { HttpAuthOption } from "@smithy/types";
/**
 * Resolves list of auth options based on the supported ones, vs the preference list.
 *
 * @param candidateAuthOptions list of supported auth options selected by the standard
 *   resolution process (model-based, endpoints 2.0, etc.)
 * @param authSchemePreference list of auth schemes preferred by user.
 * @returns
 */
export declare const resolveAuthOptions: (candidateAuthOptions: HttpAuthOption[], authSchemePreference: string[]) => HttpAuthOption[];
