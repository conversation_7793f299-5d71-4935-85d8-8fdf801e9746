// require('dotenv').config();
const { S3Client, ListObjectsV2Command, PutObjectCommand } = require('@aws-sdk/client-s3');
const fs = require('fs');

const BUCKET_NAME = 'uncomplicate';
const ACCESS_KEY = '********************';
const SECRET_KEY = 'o66+KKcmXGk/l0KZDaZ/IkKHaaUnvKaw/IVTs8f9';

async function testConnectionAndUpload() {
    console.log('Testing S3 connection and upload...');
    console.log(`Bucket: ${BUCKET_NAME}`);

    // Try different regions
    const regions = ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-south-1', 'ap-southeast-1'];

    for (const region of regions) {
        try {
            console.log(`Trying region: ${region}`);

            const s3Client = new S3Client({
                region: region,
                credentials: {
                    accessKeyId: ACCESS_KEY,
                    secretAccessKey: SECRET_KEY,
                },
            });

            // Test connection first
            const listCommand = new ListObjectsV2Command({
                Bucket: BUCKET_NAME,
            });

            const listResponse = await s3Client.send(listCommand);
            console.log(`✅ Connected in region: ${region}`);
            console.log(`Files found: ${listResponse.Contents ? listResponse.Contents.length : 0}`);

            // Now test upload
            console.log('Testing upload...');

            // Create a test file
            const testContent = `Hello from S3 upload test!\nTimestamp: ${new Date().toISOString()}`;
            fs.writeFileSync('test.txt', testContent);

            // Upload to folder/test.txt
            const uploadCommand = new PutObjectCommand({
                Bucket: BUCKET_NAME,
                Key: 'uploads/test.txt', // This creates the folder "uploads"
                Body: fs.readFileSync('test.txt'),
                ContentType: 'text/plain',
            });

            const uploadResponse = await s3Client.send(uploadCommand);
            console.log('✅ File uploaded successfully to uploads/test.txt');

            return; // Success, exit

        } catch (error) {
            console.log(`❌ Failed in ${region}: ${error.message}`);
        }
    }

    console.log('❌ Could not connect to any region');
}

testConnection();