// require('dotenv').config();
const { S3Client, ListObjectsV2Command } = require('@aws-sdk/client-s3');

const BUCKET_NAME = 'uncomplicate';
const ACCESS_KEY = '********************';
const SECRET_KEY = 'o66+KKcmXGk/l0KZDaZ/IkKHaaUnvKaw/IVTs8f9';

async function testConnection() {
    console.log('Testing S3 connection...');
    console.log(`Bucket: ${BUCKET_NAME}`);

    // Try different regions
    const regions = ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-south-1', 'ap-southeast-1'];

    for (const region of regions) {
        try {
            console.log(`Trying region: ${region}`);

            const s3Client = new S3Client({
                region: region,
                credentials: {
                    accessKeyId: ACCESS_KEY,
                    secretAccessKey: SECRET_KEY,
                },
            });

            const command = new ListObjectsV2Command({
                Bucket: BUCKET_NAME,
            });

            const response = await s3Client.send(command);
            console.log(`✅ SUCCESS! Connected in region: ${region}`);
            console.log(`Files found: ${response.Contents ? response.Contents.length : 0}`);
            return; // Success, exit

        } catch (error) {
            console.log(`❌ Failed in ${region}`);
        }
    }

    console.log('❌ Could not connect to any region');
}

testConnection();