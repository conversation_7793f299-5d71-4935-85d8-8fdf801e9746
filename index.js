require('dotenv').config();
const { S3Client, ListObjectsV2Command } = require('@aws-sdk/client-s3');

const s3Client = new S3Client({
    region: 'us-east-1', // Change this to your region
    credentials: {
        accessKeyId: 'YOUR_ACCESS_KEY',     // Replace with your access key
        secretAccessKey: 'YOUR_SECRET_KEY', // Replace with your secret key
    },
});

const BUCKET_NAME = 'your-bucket-name'; // Replace with your bucket name

async function testConnection() {
    try {
        console.log('Testing S3 connection...');

        const command = new ListObjectsV2Command({
            Bucket: BUCKET_NAME,
        });

        const response = await s3Client.send(command);
        console.log('✅ Connected to S3 successfully!');
        console.log(`Bucket: ${BUCKET_NAME}`);
        console.log(`Files found: ${response.Contents ? response.Contents.length : 0}`);

    } catch (error) {
        console.log('❌ Connection failed:', error.message);
    }
}

testConnection();