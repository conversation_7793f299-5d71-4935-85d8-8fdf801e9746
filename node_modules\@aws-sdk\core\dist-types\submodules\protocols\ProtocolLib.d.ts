import { ErrorSchema, NormalizedSchema, TypeRegistry } from "@smithy/core/schema";
import type { HttpResponse as IHttpResponse, MetadataBearer, ResponseMetadata, SerdeFunctions } from "@smithy/types";
/**
 * @internal
 */
type ErrorMetadataBearer = MetadataBearer & {
    $response: IHttpResponse;
    $fault: "client" | "server";
};
/**
 * Shared code for Protocols.
 *
 * @internal
 */
export declare class ProtocolLib {
    /**
     * @param body - to be inspected.
     * @param serdeContext - this is a subset type but in practice is the client.config having a property called bodyLengthChecker.
     *
     * @returns content-length value for the body if possible.
     * @throws Error and should be caught and handled if not possible to determine length.
     */
    calculateContentLength(body: any, serdeContext?: SerdeFunctions): string;
    /**
     * This is only for REST protocols.
     *
     * @param defaultContentType - of the protocol.
     * @param inputSchema - schema for which to determine content type.
     *
     * @returns content-type header value or undefined when not applicable.
     */
    resolveRestContentType(defaultContentType: string, inputSchema: NormalizedSchema): string | undefined;
    /**
     * Shared code for finding error schema or throwing an unmodeled base error.
     * @returns error schema and error metadata.
     *
     * @throws ServiceBaseException or generic Error if no error schema could be found.
     */
    getErrorSchemaOrThrowBaseException(errorIdentifier: string, defaultNamespace: string, response: IHttpResponse, dataObject: any, metadata: ResponseMetadata, getErrorSchema?: (registry: TypeRegistry, errorName: string) => ErrorSchema): Promise<{
        errorSchema: ErrorSchema;
        errorMetadata: ErrorMetadataBearer;
    }>;
    /**
     * Reads the x-amzn-query-error header for awsQuery compatibility.
     *
     * @param output - values that will be assigned to an error object.
     * @param response - from which to read awsQueryError headers.
     */
    setQueryCompatError(output: Record<string, any>, response: IHttpResponse): void;
    /**
     * Assigns Error, Type, Code from the awsQuery error object to the output error object.
     * @param queryCompatErrorData - query compat error object.
     * @param errorData - canonical error object returned to the caller.
     */
    queryCompatOutput(queryCompatErrorData: any, errorData: any): void;
}
export {};
